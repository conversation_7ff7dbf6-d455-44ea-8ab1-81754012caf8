@inherits HeaderLayoutBase

<header class="mobile-header">
    <a href="/orderlist">
        @if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.MobileLogo))
        {
            <Logo />
        }
        else
        {
            <div class="main-logo">
                <img src="@MainLayout.SiteSettings.MobileLogo" />
            </div>
        }

    </a>

    @if (MainLayout.SiteSettings.MarketplaceEnabled && MainLayout.UserSettings.UserSecurity.IsMarketingAdmin == false)
    {
        <div class="center-search-area">
            <div class="autocomplete-container">
                @if (this.SelectedCategory.Id != Guid.Empty)
                {
                    <div class="search-scope">
                        Search in <span @onclick=@RemoveSelectedCategory><span>@this.SelectedCategory.Name</span> <span>&times;</span></span>
                    </div>
                }
                else
                {
                    <div class="search-scope">
                        Search by Products or Supplier
                    </div>
                }
                <div class="search-wrap">
                    <input type="search" @bind-value=SearchText @bind-value:event="oninput" @onkeyup="@this.SearchQueryKeyUp" placeholer="Search by Products or Supplier" />
                    @if (this.SuggestedTerms.Any())
                    {
                        <span class="cancel-search" @onclick="ClearSearchSuggestions">Clear Search</span>
                    }
                    <ul>
                        @foreach (var suggestedTerm in SuggestedTerms)
                        {
                            <li @onclick="@(() => SearchSuggestionClick(suggestedTerm))">
                                <div class="description">@suggestedTerm</div>
                            </li>
                        }
                    </ul>
                    <span class="button" @onclick=@this.Search>
                        <IconSearch />
                    </span>
                </div>
            </div>
        </div>
        <a class="basket-mobile" href="/shoppingbasket">
            @if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.BasketIconWhite))
            {
                <IconBasket />
            }
            else
            {
                @((MarkupString)MainLayout.SiteSettings.Icons.BasketIconWhite)
            }
        </a>

    }
    else
    {
        <div style="flex-grow:1"></div>
    }
    <OpenECX.Blazor.Shared.Layout.Sidebar.MobileNavMenu />
</header>
@if (MainLayout.SiteSettings.MarketplaceEnabled)
{
    <header class="desktop-header">

        <div class="hidden-logo" hidden="@(!MainLayout._SidebarLayout.SidebarMenuHidden)">
            <Logo />
        </div>

        @if (MainLayout.UserSettings.UserSecurity.IsMarketingAdmin == false)
        {
            <div class="center-search-area">
                <div class="autocomplete-container">
                    @if (this.SelectedCategory.Id != Guid.Empty)
                    {
                        <div class="search-scope">
                            Search in <span @onclick=@RemoveSelectedCategory>@this.SelectedCategory.Name <span>&times;</span></span>
                        </div>
                    }
                    else
                    {
                        <div class="search-scope">
                            All
                        </div>
                    }
                    <div class="search-wrap">
                        <input type="search" @bind-value=SearchText @bind-value:event="oninput" @onkeyup="@this.SearchQueryKeyUp" placeholder="Search by Products or Supplier" />

                        @if (this.SuggestedTerms.Any())
                        {
                            <span class="cancel-search" @onclick="ClearSearchSuggestions">Clear Search</span>
                        }
                        <ul>
                            @foreach (var suggestedTerm in SuggestedTerms)
                            {
                                <li @onclick="@(() => SearchSuggestionClick(suggestedTerm))">
                                    <div class="description">@suggestedTerm</div>
                                </li>
                            }
                        </ul>
                    </div>
                </div>
                <span class="button" @onclick=@this.Search>
                    <IconSearch />
                </span>
            </div>

            @if (this.MainLayout.UserSettings.UserSecurity.IsSupplierBasicPIM == false)
            {
                <div class="topbutton-container" @onclick=@(() => ToggleShowPrices())>
                    @if (ShowPrices)
                    {
                        if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.HidePriceIconWhite))
                        {
                            <IconHide />
                        }
                        else
                        {
                            @((MarkupString)MainLayout.SiteSettings.Icons.HidePriceIconWhite)
                        }

                        <span class="button">Hide Prices</span>
                    }
                    else
                    {
                        if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.ShowPriceIconWhite))
                        {
                            <IconView />
                        }
                        else
                        {
                            @((MarkupString)MainLayout.SiteSettings.Icons.ShowPriceIconWhite)
                        }

                        <span class="button">Show Prices</span>
                    }
                </div>
            
                <div class="topbutton-container condensable">
                    @if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.FavouriteIconWhite))
                    {
                        <IconStarBig />
                    }
                    else
                    {
                        @((MarkupString)MainLayout.SiteSettings.Icons.FavouriteIconWhite)
                    }


                    <a class="button" href="user/favourites"><span>Favourites</span></a>
                </div>
            
                @if (MainLayout.UserSettings.CompareProductCount > 0)
                {
                    <div class="topbutton-container condensable">
                        @if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.CompareIconWhite))
                        {
                            <IconPallet />
                        }
                        else
                        {
                            @((MarkupString)MainLayout.SiteSettings.Icons.CompareIconWhite)
                        }

                        <span class="inline-number">@MainLayout.UserSettings.CompareProductCount</span>
                        <a class="button" href="/user/compare">
                            <span>Compare</span>
                        </a>
                    </div>
                }
            }
                    
            @if (MainLayout.SiteSettings.RequisitionToOrderEnabled)
            {
                <div class="topbutton-container">
                    <IconProduct />
                    <a class="button" href="requisitions"><span>Requisitions</span></a>
                </div>
            }

            @if (MainLayout.UserSettings.UserSecurity.UserPermissions.CanViewOrders)
            {
                <div class="topbutton-container">
                    @if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.OrderIconWhite))
                    {
                        <IconProduct />
                    }
                    else
                    {
                        @((MarkupString)MainLayout.SiteSettings.Icons.OrderIconWhite)
                    }

                    <a class="button" href="orderlist"><span>Orders</span></a>
                </div>   
            }

            @if (MainLayout.UserSettings.UserSecurity.UserPermissions.CanAddOrders && MainLayout.SiteSettings.RequisitionToOrderEnabled == false)
            {
                <div class="topbutton-container">
                    @if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.QuickOrderIconWhite))
                    {
                        <IconOrders />
                    }
                    else
                    {
                        @((MarkupString)MainLayout.SiteSettings.Icons.QuickOrderIconWhite)
                    }

                    @if (MainLayout.UserSettings.UserSecurity.AllowOrderUploads)
                    {
                        <DropButtonControl Text="Quick Order">
                            <li>
                                <a href="/orders/order/00000000-0000-0000-0000-000000000000">Quick Order</a>
                            </li>
                            <li>
                                <a href="/Orders/FromFile/">Upload Order from File</a>
                            </li>
                        </DropButtonControl>
                    }
                    else
                    {
                        <a class="button" href="/orders/order/00000000-0000-0000-0000-000000000000"><span>Quick Order</span></a>
                    }
                    
                </div>
            }

            <div class="topbutton-container notifications-container condensable">

                @if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.NotificationIconWhite))
                {
                    <IconNotifications />
                }
                else
                {
                    @((MarkupString)MainLayout.SiteSettings.Icons.NotificationIconWhite)
                }

                @if (MainLayout.UserSettings.UserNotifications.Total != 0)
                {
                    <span class="inline-number">@MainLayout.UserSettings.UserNotifications.Total</span>

                    <DropButtonControl Text="">

                        <li><span class="fake-button" @onclick="MarkNoticationsAsRead">Mark all as read</span></li>

                        <li>
                            <a href="user/listnotifications/0">All<span class="inline-total">@MainLayout.UserSettings.UserNotifications.Total</span></a>
                        </li>

                        @foreach (var group in MainLayout.UserSettings.UserNotifications.NotificationGroups.Where(s => s.Total > 0))
                        {
                            <li>
                                <a href="user/listnotifications/@group.Id">@group.Name<span class="inline-total">@group.Total</span></a>
                            </li>
                        }
                    </DropButtonControl>
                }
                else
                {
                    <a class="button" href="user/listnotifications/0"><span></span></a>
                }
            </div>
        }
        
        <div class="topbutton-container condensable">
            @if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.MyAccountIconWhite))
            {
                <IconAccount />
            }
            else
            {
                @((MarkupString)MainLayout.SiteSettings.Icons.MyAccountIconWhite)
            }

            <DropButtonControl Text="My Account">
                <li>
                    <a href="user/details">Profile</a>
                </li>
                @if (MainLayout.SiteSettings.MarketplaceEnabled && MainLayout.UserSettings.UserSecurity.IsSupplierBasicPIM == false)
                {
                    <li>
                        <a href="user/customertabs">@MainLayout.SiteSettings.Text.CustomerSingular Account</a>
                    </li>
                }
                @if (MainLayout.UserSettings.UserSecurity.IsSupplierAdmin && MainLayout.SiteSettings.SupplierProfilesEnabled)
                {
                    <li>
                        <a href=@($"admin/editsupplier/{MainLayout.UserSettings.UserSecurity.SupplierId}")>Supplier Account</a>
                    </li>
                }
                <li>
                    <a href="account/logout">Log Out</a>
                </li>
            </DropButtonControl>
        </div>

        @if (MainLayout.UserSettings.UserSecurity.IsMarketingAdmin == false && 
            MainLayout.UserSettings.UserSecurity.IsSupplierReadOnly == false &&
            MainLayout.UserSettings.UserSecurity.IsSupplierBasicPIM == false)
        {
            <div class="topbutton-container">
                @if (string.IsNullOrWhiteSpace(MainLayout.SiteSettings.Icons.BasketIconWhite))
                {
                    <IconBasket />
                }
                else
                {
                    @((MarkupString)MainLayout.SiteSettings.Icons.BasketIconWhite)
                }

                @if (MainLayout.SiteSettings.RequisitionToOrderEnabled)
                {
                    <span class='inline-number'>@this.MainLayout.UserSettings.RequisitionsBasketCount</span>
                }
                else
                {
                    <span class='inline-number'>@this.MainLayout.UserSettings.OrdersBasketCount</span>
                }

                <a class="button" @onclick="@this.LoadBaskets">
                    Basket
                </a>
            </div>
        }
    </header>

}