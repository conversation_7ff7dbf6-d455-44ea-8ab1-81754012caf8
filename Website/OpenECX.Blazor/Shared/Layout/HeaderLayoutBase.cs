﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using OpenECX.Blazor.Components.Base;
using OpenECX.Blazor.Pages.Marketplace.ShoppingBasket;
using OpenECX.Shared.Notification;
using OpenECX.Shared.Settings;
using OpenECX.Shared.ShoppingBasket;
using OpenECX.Blazor.Services;
using OpenECX.Shared.Product;
using OpenECX.Shared.Search;

namespace OpenECX.Blazor.Shared.Layout
{
    public class HeaderLayoutBase : ComponentServiceBase
    {
        [Inject]
        public NotificationService NotificationService { get; set; }

        [CascadingParameter] public MainLayout MainLayout { get; set; }

        protected async override Task OnInitializedAsync()
        {
            if (this.CurrentUserService.IsLoggedIn && (this.MainLayout.SiteSettings.MarketplaceEnabled || this.MainLayout.SiteSettings.RequisitionToOrderEnabled))
            {
                await this.GetShoppingBasketTotalsAsync();
            }
        }

        #region Categories

        public CategoryModel SelectedCategory { get; set; } = new CategoryModel();

        public void SetSelectedCategory(CategoryModel categoryModel)
        {
            this.SelectedCategory = categoryModel;
            this.StateHasChanged();
        }

        public void RemoveSelectedCategory()
        {
            this.SelectedCategory = new CategoryModel();
            this.Search();
        }

        #endregion

        #region Search

        public string SearchText { get; set; } = string.Empty;
        public List<string> SuggestedTerms { get; set; } = new List<string>();
        public List<ProductListItemModel> SuggestedProducts { get; set; } = new List<ProductListItemModel>();
        public bool IsSearching { get; set; }

        public void Search()
        {
            this.ClearSearchSuggestions();
            this.IsSearching = true;
            if (this.SelectedCategory.Id != Guid.Empty)
            {
                this.NavigationManager.NavigateTo($"/SearchCategory/{this.SelectedCategory.Id}/{Uri.EscapeDataString(this.SearchText)}", false);
            }
            else
            {
                this.NavigationManager.NavigateTo($"/Search/{Uri.EscapeDataString(this.SearchText)}", false);
            }
        }

        public async Task SearchQueryKeyUp(KeyboardEventArgs e)
        {
            this.ClearSearchSuggestions();

            if (!string.IsNullOrWhiteSpace(this.SearchText))
            {
                // Get search suggestions
                var suggestions = this.ApiService.PostJson<List<string>>("/Product/SearchSuggestions?searchTerm=" + Uri.EscapeDataString(this.SearchText), new { });
                this.SuggestedTerms.AddRange(suggestions);

                // Get product results for autocomplete
                var searchRequest = new SearchRequestModel
                {
                    SearchTerm = this.SearchText,
                    CategoryId = this.SelectedCategory.Id,
                    PagerGridParameters = new BlazorComponents.Models.Component.PagerGridParameters
                    {
                        ItemsPerPage = 5, // Limit to 5 products for autocomplete
                        SelectedPageIndex = 0
                    }
                };

                var searchResults = await this.ApiService.PostJsonAsync<SearchModel>("/Product/SearchProducts", searchRequest);
                if (searchResults?.PagerGridResult?.Results != null)
                {
                    this.SuggestedProducts.AddRange(searchResults.PagerGridResult.Results.Take(5));
                }

                this.StateHasChanged();
            }

            if (e.Code == "Enter" || e.Code == "NumpadEnter")
            {
                this.Search();
            }
        }

        public void SearchSuggestionClick(string suggestedTerm)
        {
            this.ClearSearchSuggestions();
            this.SearchText = suggestedTerm;
            this.Search();
        }

        public void SearchProductClick(ProductListItemModel product)
        {
            this.ClearSearchSuggestions();
            this.NavigationManager.NavigateTo($"/product/{product.Id}", false);
        }

        public void ClearSearchSuggestions()
        {
            this.SuggestedTerms.Clear();
            this.SuggestedProducts.Clear();
        }

        #endregion

        #region Prices

        public bool ShowPrices { get; set; } = true;
        public void ToggleShowPrices()
        {
            this.ShowPrices = !this.ShowPrices;
            this.MainLayout.Refresh();
        }

        #endregion

        #region Compare

        public int CompareCount { get; set; }

        #endregion

        #region Notifications

        public async Task MarkNoticationsAsRead()
        {
            var success = await this.ApiService.PostJsonAsync<bool>("/Notification/MarkUserNotificationsAsRead");
            await this.GetUserNotifications();

            await NotificationService.MarkNotificationsAsRead();
        }

        public async Task GetUserNotifications()
        {
            this.MainLayout.UserSettings.UserNotifications = await this.ApiService.GetJsonAsync<UserNotificationsModel>("/Notification/GetUserNotifications");
            this.StateHasChanged();
        }

        #endregion

        #region Basket

        public ShoppingBasketPageBase ShoppingBasketPageBase { get; set; }

        public async Task LoadBaskets()
        {
            if (this.ShoppingBasketPageBase != null)
            {
                await this.ShoppingBasketPageBase.GetData();
            }
            else
            {
                this.NavigationManager.NavigateTo("/shoppingbasket");
            }
        }

        public void UpdateProductComparisonCount(bool added)
        {
            if (added)
            {
                this.MainLayout.UserSettings.CompareProductCount += 1;
            }
            else
            {
                this.MainLayout.UserSettings.CompareProductCount -= 1;
            }

            this.StateHasChanged();
        }

        public async Task GetShoppingBasketTotalsAsync()
        {
            var shoppingBasketTotals = await this.ApiService.PostJsonAsync<ShoppingBasketTotalsModel>("/ShoppingBasket/GetShoppingBasketTotals");
            this.MainLayout.UserSettings.OrdersBasketCount = shoppingBasketTotals.OrdersBasketCount;
            this.MainLayout.UserSettings.RequisitionsBasketCount = shoppingBasketTotals.RequisitionsBasketCount;

            this.StateHasChanged();
        }

        public void ClearShoppingBasketCount()
        {
            this.MainLayout.UserSettings.OrdersBasketCount = 0;
            this.MainLayout.UserSettings.RequisitionsBasketCount = 0;
            this.StateHasChanged();
        }

        #endregion

    }
}
