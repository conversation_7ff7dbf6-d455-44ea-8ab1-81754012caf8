﻿@inherits ViewMatchingBase
@page "/ehub/matching"


<Header>
    <Titles>
        <IconOrders /><span class="title">Matching</span>
    </Titles>
    <Buttons>
        @if (this.MainLayout.UserSettings.UserSecurity.IsAdmin && this.CustomerList != null)
        {
            <div class="r header-supplier-select">
                <DropDownListEditor @bind-Value=@SelectedCustomerId Items=@CustomerList Label="Customer" OnChangeActionAsync="CustomerSelected" />
            </div>
        }
    </Buttons>
</Header>

@if (this.LoadingNewCustomer)
{
    <Loading />
}
else
{
    <TabControl @ref="TabControl" OnTabSelected="this.OnTabChange">

    @if (this.MainLayout.UserSettings.UserSecurity.IsSuperAdmin
       || this.MainLayout.UserSettings.UserSecurity.EhubSettings.PaymentReconciliationEnabled
       || this.MainLayout.UserSettings.UserSecurity.EhubSettings.StatementReconciliationEnabled)
    {
        <TabItem Text="Statement" @ref="StatementTab">
            <div class="card">
                <OpenECX.Blazor.Pages.eHub.Matching.View.Tabs.StatementMatching.ViewStatementMatching ViewMatching="this" />
            </div>
        </TabItem>
    }

        @if (this.MainLayout.UserSettings.UserSecurity.IsSuperAdmin)
        {
            <TabItem Text="Payment Reconciliation">
                <div class="card">
                    <OpenECX.Blazor.Pages.eHub.Matching.View.Tabs.PaymentReconciliation.ViewPaymentReconciliation ViewMatching="this" />
                </div>
            </TabItem>
@* 
            <TabItem Text="Chase">
                <div class="card">
                    <OpenECX.Blazor.Pages.eHub.Matching.View.Tabs.Chase.ViewChase ViewMatching="this" />
                </div>
            </TabItem>

            <TabItem Text="Duplicates & Mismatches (10)" @ref="DuplicatesTab">
                <OpenECX.Blazor.Pages.eHub.Matching.View.Tabs.Duplicates.ViewDuplicates ViewMatching="this" />
            </TabItem> *@
        }

        @if ((this.MainLayout.UserSettings.UserSecurity.IsSuperAdmin)
            || this.MainLayout.UserSettings.UserSecurity.IsCustomerAdmin)
        {
            <TabItem Text="Settings" @ref="SettingsTab">
                <OpenECX.Blazor.Pages.eHub.Matching.View.Tabs.MatchingSettings.MatchingSettings ViewMatching="this" />
            </TabItem>
        }
    </TabControl>
}

@* <TabItem Text="Invoice">
    <div class="card">
    <OpenECX.Blazor.Pages.eHub.Matching.View.Tabs.InvoiceMatching.ViewInvoiceMatching ViewMatching="this" />
    </div>
    </TabItem>

    <TabItem Text="GRN">
    <div class="card">
    <OpenECX.Blazor.Pages.eHub.Matching.View.Tabs.GRNMatching.ViewGRNMatching ViewMatching="this" />
    </div>
    </TabItem>

    <TabItem Text="Quote">
    <div class="card">
    <OpenECX.Blazor.Pages.eHub.Matching.View.Tabs.QuoteMatching.ViewQuoteMatching ViewMatching="this" />
    </div>
    </TabItem> *@