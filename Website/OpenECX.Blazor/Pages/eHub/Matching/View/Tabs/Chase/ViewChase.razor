@inherits ViewChaseBase

<div class="title">
    Missing Statements
    <div class="r multi-input-block" style="width: 853px;">
        <div class="block">
            <label>No Statement Received Between</label>
            <input type="text" value="01/04/2024" style="width: 100px"> - <input type="text" value="30/04/2024" style="width: 100px">
        </div>
        <div class="block">
            <label>Due Date</label>
            <input type="text" value="01/04/2024" style="width: 100px"> - <input type="text" value="30/04/2024" style="width: 100px">
        </div>
    </div>
    <div class="button" @onclick="ToggleDialog">Chase (@(showClicked ? "2" : "0"))</div>
</div>



<TabControl>
    <TabItem Text="All">
        <div class="top-selects" @onclick="ToggleClicked">
            <div class="form-group block  check force-left">
                <label for="AllSelected">Select All Records <span hidden="" class="mandatory">*</span></label>
                <div class="form-group block  check force-left"><div class="checkbox  @(showClicked ? "selected" : "") " b-w8wictzkwz=""><!--!--><input type="checkbox" id="IsSelected" name="IsSelected" class="form-control" b-w8wictzkwz=""></div></div>
            </div>
            <div class="form-group block  check force-left">
                <label for="AllPageSelected">Select Records on This Page <span hidden="" class="mandatory">*</span></label>
                <div class="form-group block  check force-left"><div class="checkbox  @(showClicked ? "selected" : "") " b-w8wictzkwz=""><!--!--><input type="checkbox" id="IsSelected" name="IsSelected" class="form-control" b-w8wictzkwz=""></div></div>
            </div>
        </div>


        <table class="table">
            <thead>
                <tr>
                    <th style="width: 20px;"></th>
                    <th style="width: 20px;"></th>
                    <th>Due Date</th>
                    <th>Supplier</th>
                    <th>Transactions</th>
                    <th>Reason</th>
                    <th>Total</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr class="tr-group @(showMissing1 ? "" : "collapsed")">
                    <td><div class="arrow" @onclick="ToggleMissing1"></div></td>
                    <td><div class="form-group block  check force-left"><div class="checkbox @(showClicked ? "selected" : "")  " b-w8wictzkwz=""><input type="checkbox" id="IsSelected" name="IsSelected" class="form-control" b-w8wictzkwz=""></div></div></td>
                    <td></td>
                    <td>Supplier A</td>
                    <td>4</td>
                    <td></td>
                    <td>£45,202.30</td>
                    <td><div class="button secondary small">Chase</div></td>
                </tr>
                @if (showMissing1)
                {
                    <tr>
                        <td></td>
                        <td></td>
                        <td>30th April 2024</td>
                        <td>SRS1T101-INVS1T1G6</td>
                        <td></td>
                        <td>Document Not Found</td>
                        <td>£12,459.45</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td>30th April 2024</td>
                        <td>SRS1T101-INVS1T1G6</td>
                        <td></td>
                        <td>Document Not Found</td>
                        <td>£8,932.46</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td>30th April 2024</td>
                        <td>SRS1T101-INVS1T1G6</td>
                        <td></td>
                        <td>Matched with different total</td>
                        <td>£5,291.95</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td>30th April 2024</td>
                        <td>SRS1T101-INVS1T1G6</td>
                        <td></td>
                        <td>Document Not Found</td>
                        <td>£18,518.44</td>
                        <td></td>
                    </tr>
                }
                <tr class="tr-group @(showMissing2 ? "" : "collapsed")">
                    <td><div class="arrow" @onclick="ToggleMissing2"></div></td>
                    <td><div class="form-group block  check force-left"><div class="checkbox @(showClicked ? "selected" : "")  " b-w8wictzkwz=""><input type="checkbox" id="IsSelected" name="IsSelected" class="form-control" b-w8wictzkwz=""></div></div></td>
                    <td></td>
                    <td>Supplier B</td>
                    <td>40</td>
                    <td></td>
                    <td>£76,235.49</td>
                    <td><div class="button secondary small">Chase</div></td>
                </tr>
                @if (showMissing2)
                {
                    <tr>
                        <td></td>
                        <td></td>
                        <td>30th April 2024</td>
                        <td>SRS1T101-INVS1T1G6</td>
                        <td></td>
                        <td>Matched with different total</td>
                        <td>£12,459.45</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td>30th April 2024</td>
                        <td>SRS1T101-INVS1T1G6</td>
                        <td></td>
                        <td>Document Not Found</td>
                        <td>£8,932.46</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td>30th April 2024</td>
                        <td>SRS1T101-INVS1T1G6</td>
                        <td></td>
                        <td>In Query</td>
                        <td>£5,291.95</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td>30th April 2024</td>
                        <td>SRS1T101-INVS1T1G6</td>
                        <td></td>
                        <td>Matched with different total</td>
                        <td>£18,518.44</td>
                        <td></td>
                    </tr>
                }
            </tbody>
        </table>






    </TabItem>
</TabControl>

<DialogControl Title="Chase" @bind-IsOpen="ChaseDialogOpen">
    <OpenECX.Blazor.Pages.eHub.Matching.View.Tabs.Chase.ChaseEmailDialog.ChaseEmailDialog ViewChase="this" />
</DialogControl>
