﻿using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BlazorComponents.Models.Common;
using BlazorComponents.Models.Component;
using BlazorComponents.Models.PagerGridControl;
using BlazorComponents.Services;
using Microsoft.AspNetCore.Components;
using OpenECX.Blazor.Components.Base;
using OpenECX.Shared.Approval;

namespace OpenECX.Blazor.Pages.Admin.Approval.View.PriceChangeApproval
{
    public class _PriceChangesApprovalBase : ComponentServiceBase
    {
        [Parameter]
        public ViewApprovalModel ViewApprovalModel { get; set; }

        public bool PrioritySuppliers { get; set; }

        public async Task<PagerGridResult<IEnumerable<ApprovalPriceChangeModel>>> GetData(PagerGridParameters pagerGridParameters)
        {
            var gridResults = this.ViewApprovalModel.PriceChanges.AsQueryable();

            return await ComponentOperations.CreateGridAsync(gridResults.AsQueryable(), pagerGridParameters);
        }

        public List<SelectListItem> SortableItems => new ()
        {
            new SelectListItem { Text = "Part Number", Value = "PartNumber" },
            new SelectListItem { Text = "Name", Value = "Name" },
            new SelectListItem { Text = "Effective Date", Value = "EffectiveDate" },
            new SelectListItem { Text = "Price", Value = "Price" },
        };

        public List<SelectListItem> SearchableItems => new ()
        {
            new SelectListItem { Text = "Part Number", Value = "PartNumber" },
            new SelectListItem { Text = "Name", Value = "Name" },
        };

        public PagerExportModel ExportDataMapping(IEnumerable<ApprovalPriceChangeModel> results)
        {
            var columnHeaders = new List<string> { "Part Number", "Name", "	Effective Date", "Price" };

            var sb = new StringBuilder();

            foreach (var item in results)
            {
                sb.AppendLine(string.Join(",", new List<string>
                {
                    item.PartNumber,
                    item.Name,
                    item.EffectiveDate.ToString("dd/MM/yyyy HH:mm"),
                    item.Price.ToString("0.00"),
                }));
            }

            return new PagerExportModel(columnHeaders, sb, "PriceApprovals");
        }
    }
}
