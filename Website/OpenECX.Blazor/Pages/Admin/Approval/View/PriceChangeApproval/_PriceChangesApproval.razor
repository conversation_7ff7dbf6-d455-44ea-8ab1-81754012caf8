@inherits _PriceChangesApprovalBase
@using OpenECX.Shared.Enums;

<div class="card-header mb-3">
    <div class="flex align-center">
        <h3 class="mr-3">Price Changes Approval</h3>
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="prioritySuppliers" @bind="PrioritySuppliers">
            <label class="form-check-label" for="prioritySuppliers">
                Priority Suppliers
            </label>
        </div>
    </div>
</div>

    <PagerGridControl DataRequestFuncAsync=@GetData
                      SortableItems=@SortableItems
                      SearchableItems=@SearchableItems
                      ExportDataMappingFunc="ExportDataMapping">
        <HeaderTemplate>
            <th>Part Number</th>
            <th>Name</th>
            <th>Effective Date</th>
            <th>Price(s)</th>
            <th>Quantity Breaks</th>
        </HeaderTemplate>
        <ResultsTemplate>
            <td>@context.PartNumber</td>
            <td>@context.Name</td>
            <td>@context.EffectiveDate</td>
            <td>
                @if (context.QuantityBreakType != (int)ProductPriceQtyBreakType.None && context.QuantityBreaks.Any())
                {
                    foreach (var qtyBreak in context.QuantityBreaks.OrderBy(o => o.QuantityBreak))
                    {
                        <div class="mini-price-grid">
                            <span>@qtyBreak.QuantityBreak.ToString() or More</span>
                            <span>@qtyBreak.Price.ToMoney()</span>
                        </div>
                    }
                }
                else
                {
                    <div><span class="single-price">@context.Price.ToMoney()</span></div>
                }
                    </td>
            <td>@context.QuantityBreakTypeString</td>
            <td></td>
        </ResultsTemplate>
    </PagerGridControl>
